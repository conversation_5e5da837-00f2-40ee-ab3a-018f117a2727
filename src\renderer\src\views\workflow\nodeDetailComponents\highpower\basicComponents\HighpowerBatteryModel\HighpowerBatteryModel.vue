<template>
  <div>
    <BatteryModelCard title="豪鹏电池模型">
      <template #actions>
        <Button
          v-if="dataLoaded && !modelBuilt"
          variant="default"
          size="sm"
          :disabled="isBuilding"
          @click="buildModel"
        >
          <LucideIcon v-if="!isBuilding" name="Settings" class="mr-1 h-4 w-4" />
          <span v-if="isBuilding" class="animate-spin mr-1">
            <LucideIcon name="RefreshCcw" class="h-4 w-4" />
          </span>
          {{ isBuilding ? '构建中...' : '构建模型' }}
        </Button>
        <Button
          v-if="dataLoaded && modelBuilt"
          variant="outline"
          size="sm"
          @click="handleEditModel"
        >
          <LucideIcon name="Edit2" class="mr-1 h-4 w-4" />
          修改模型
        </Button>
        <Button v-if="dataLoaded" variant="destructive" size="sm" @click="resetData">
          重新上传
        </Button>
      </template>

      <!-- 模型构建状态 -->
      <ModelBuilder :model-built="modelBuilt" :is-building="isBuilding" :progress="buildProgress" />

      <!-- 文件上传组件 -->
      <FileUploader
        v-if="!isUploading && !dataLoaded"
        :accept-types="['.model']"
        :max-size="20"
        @file-data="handleFileData"
        @error="handleUploadError"
        @progress="handleProgress"
        @upload-complete="handleUploadComplete"
      />

      <!-- 显示解析结果 -->
      <div v-if="dataLoaded" class="mt-2">
        <Tabs :default-value="'battery'" class="space-y-2">
          <TabsList class="flex flex-row space-x-2">
            <TabsTrigger value="battery" class="w-1/2">电化学参数</TabsTrigger>
            <TabsTrigger value="aging" class="w-1/2">老化参数</TabsTrigger>
          </TabsList>

          <TabsContent value="battery">
            <BatteryParameterList
              title="电化学参数"
              :parameters="batteryParameters"
              :editable="!modelBuilt"
              @edit="openEditDialog('Battery parameters', $event)"
            />
          </TabsContent>

          <TabsContent value="aging">
            <BatteryParameterList
              title="老化参数"
              :parameters="agingParameters"
              :editable="!modelBuilt"
              @edit="openEditDialog('Aging parameters', $event)"
            />
          </TabsContent>
        </Tabs>
      </div>
    </BatteryModelCard>

    <!-- 参数编辑对话框 -->
    <BatteryParameterEditor
      v-model:is-open="isEditDialogOpen"
      :parameter="currentParam"
      @save="handleSaveEdit"
    />
  </div>
</template>

<script setup lang="ts">
import { decode } from '@msgpack/msgpack'
import { LucideIcon } from '@renderer/components'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { useTaskStore } from '@renderer/store'
import {
  deleteNodeParams,
  getNodeParams,
  saveNodeParams,
  getInputNodesByType,
} from '@renderer/utils/nodeUtils'
import { inputParse } from '@renderer/utils/rpcParser'
import { useEventListener } from '@vueuse/core'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import {
  BatteryModelCard,
  BatteryParameterEditor,
  BatteryParameterList,
  FileUploader,
  ModelBuilder,
} from './components'

interface Parameter {
  zh_description: string
  en_description: string
  value: number
  min: number
  max: number
  param_name: string
  is_recommended?: boolean
}

interface ModelResult {
  'Battery parameters': Parameter[]
  'Aging parameters': Parameter[]
}

const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})

const taskStore = useTaskStore()
const taskService = createTaskService()

// 状态变量
const isUploading = ref(false)
const dataLoaded = ref(false)
const result = ref<ModelResult>({
  'Battery parameters': [],
  'Aging parameters': [],
})
const dataSaved = ref(false) // 数据是否已保存

// 标定参数状态
const calibrationParams = ref<any>({})

// 获取标定参数
const loadCalibrationParams = async () => {
  try {
    // 使用 getInputNodesByType 获取标定节点的参数
    const calibrationNodes = await getInputNodesByType(
      props.nodeData as any,
      'HighpowerBatteryModelCalibration',
    )

    if (calibrationNodes.length > 0) {
      // 取第一个匹配的标定节点的参数
      calibrationParams.value = calibrationNodes[0].params || {}
    } else {
      // 如果没有找到标定节点，尝试从当前节点参数中获取（向后兼容）
      const params = await getNodeParams(props.nodeData as any)
      calibrationParams.value = params?.HighpowerBatteryModelCalibration || {}
    }
  } catch (error) {
    console.error('获取标定参数失败:', error)
    calibrationParams.value = {}
  }
}

const elecResultData = computed(() => {
  const elecResultStr = calibrationParams.value?.elecResult
  if (!elecResultStr) return null
  try {
    return typeof elecResultStr === 'string' ? JSON.parse(elecResultStr) : elecResultStr
  } catch (e) {
    console.error('解析电化学参数失败:', e)
    return null
  }
})

const agingResultData = computed(() => {
  const agingResultStr = calibrationParams.value?.agingResult
  if (!agingResultStr) return null
  try {
    return typeof agingResultStr === 'string' ? JSON.parse(agingResultStr) : agingResultStr
  } catch (e) {
    console.error('解析老化参数失败:', e)
    return null
  }
})

const calibrationAllParams = computed(() => calibrationParams.value?.all_params || '')

console.log('calibrationParams====', calibrationParams.value)
const batteryParameters = computed(() => result.value['Battery parameters'] || [])
const agingParameters = computed(() => result.value['Aging parameters'] || [])

// 模型构建相关状态
const isBuilding = ref(false)
const buildProgress = ref(0)
const modelBuilt = ref(false)
const allParamsObj = ref(null)
const all_params = ref('')

// 编辑对话框相关状态
const isEditDialogOpen = ref(false)
const currentParamType = ref('')
const currentParamIndex = ref(null)
const currentParam = ref<Parameter | null>(null)

// 页面关闭前保存
const handleBeforeUnload = () => {
  if (result.value && dataLoaded.value) {
    // 在页面关闭前尝试保存，但不等待结果
    saveModelData().catch((error) => {
      console.error('页面关闭前保存失败:', error)
    })
  }
}

// 加载节点参数
const loadParams = async () => {
  try {
    const params = await getNodeParams(props.nodeData as any)
    console.log('加载节点参数loadParams', params)

    if (params.modelData) {
      try {
        // 解析存储的字符串数据
        const parsedData = JSON.parse(params.modelData)
        // 恢复数值
        const restoredData = {
          'Battery parameters': parsedData['Battery parameters'].map((param) => ({
            ...param,
            value: Number(param.value),
            min: Number(param.min),
            max: Number(param.max),
          })),
          'Aging parameters': parsedData['Aging parameters'].map((param) => ({
            ...param,
            value: Number(param.value),
          })),
        }
        result.value = restoredData
        dataLoaded.value = true
        dataSaved.value = true

        // 恢复模型构建状态
        modelBuilt.value = params.modelBuilt || false

        if (params.all_params) {
          all_params.value = params.all_params
        }
      } catch (error) {
        console.error('恢复数据失败:', error)
      }
    }
  } catch (error) {
    console.error('加载节点参数失败:', error)
  }
}

// 保存模型数据
const saveModelData = async () => {
  if (!result.value) return

  const batteryParams = result.value['Battery parameters']
  const agingParams = result.value['Aging parameters']

  // 准备电化学参数的格式
  const allParamsData = batteryParams.map((p) => ({
    param_key: p.param_name,
    min: p.min,
    max: p.max,
    init_value: p.value,
    ...(p.is_recommended ? { is_recommended: true } : {}),
  }))

  try {
    await saveNodeParams(props.nodeData as any, {
      modelData: JSON.stringify(result.value),
      modelBuilt: modelBuilt.value,
      all_params: all_params.value ? all_params.value : JSON.stringify(allParamsData),
      elec_params: JSON.stringify(
        batteryParams.reduce((obj, p) => {
          obj[p.param_name] = p.value
          return obj
        }, {}),
      ),
      aging_params: JSON.stringify(
        agingParams.reduce((obj, p) => {
          obj[p.param_name] = p.value
          return obj
        }, {}),
      ),
    })

    dataSaved.value = true
  } catch (error) {
    console.error('保存模型数据失败:', error)
    toast.error('保存失败', {
      description: '无法保存模型数据',
    })
  }
}

// 获取标定数据
const getCalibrationData = () => {
  if (elecResultData.value && agingResultData.value) {
    all_params.value = calibrationAllParams.value || ''

    // 如果没有参数对象，则获取参数
    if (!allParamsObj.value) {
      fetchParameters().then(() => {
        processCalibrationResults(elecResultData.value, agingResultData.value)
      })
    } else {
      processCalibrationResults(elecResultData.value, agingResultData.value)
    }
  }
}

// 获取参数信息
const fetchParameters = async () => {
  try {
    const keyTypePairs = {
      params_type: 'String',
    }
    const keyValuePairs = {
      params_type: 'All',
    }

    const paramData = inputParse(keyValuePairs, keyTypePairs)
    const res = await taskService.callTask('hpGetIdentifiableParameters', '', paramData)

    if (res.status === 'Success') {
      const resultData = res.result
      // 解析参数对象
      allParamsObj.value = typeof resultData === 'string' ? JSON.parse(resultData) : resultData
      return true
    } else {
      toast.error('获取参数失败', {
        description: res.message || '无法获取参数',
      })
      return false
    }
  } catch (error) {
    toast.error('提交任务失败', {
      description: error.message || '无法获取参数',
    })
    return false
  }
}

// 处理标定结果
const processCalibrationResults = (elecResult, agingResult) => {
  if (!allParamsObj.value) return

  try {
    // 初始化结果对象
    const batteryParams = []
    const agingParams = []

    // 处理电化学参数
    if (elecResult && typeof elecResult === 'object') {
      // 遍历电化学参数对象的所有键
      Object.keys(elecResult).forEach((paramKey) => {
        // 在allParamsObj中查找匹配的参数信息
        let paramInfo = null

        for (const category of Object.keys(allParamsObj.value['电化学参数'])) {
          const foundParam = allParamsObj.value['电化学参数'][category].find(
            (p) => p.param_key === paramKey,
          )
          if (foundParam) {
            paramInfo = foundParam
            break
          }
        }

        if (paramInfo) {
          batteryParams.push({
            param_name: paramKey,
            zh_description: paramInfo.param_name,
            en_description: paramKey,
            value: elecResult[paramKey],
            min: paramInfo.min,
            max: paramInfo.max,
          })
        }
      })
    }

    // 处理老化参数
    if (agingResult && typeof agingResult === 'object') {
      // 遍历老化参数对象的所有键
      Object.keys(agingResult).forEach((paramKey) => {
        // 在allParamsObj中查找匹配的参数信息
        let paramInfo = null

        // 遍历老化参数的各个分类
        for (const category of Object.keys(allParamsObj.value['老化参数'])) {
          const foundParam = allParamsObj.value['老化参数'][category].find(
            (p) => p.param_key === paramKey,
          )
          if (foundParam) {
            paramInfo = foundParam
            break
          }
        }

        if (paramInfo) {
          agingParams.push({
            param_name: paramKey,
            zh_description: paramInfo.param_name,
            en_description: paramKey,
            value: agingResult[paramKey],
            min: paramInfo.min,
            max: paramInfo.max,
          })
        }
      })
    }

    // 更新结果
    result.value = {
      'Battery parameters': batteryParams,
      'Aging parameters': agingParams,
    }

    // 更新状态
    dataLoaded.value = true

    // 保存处理后的数据
    saveModelData()
  } catch (error) {
    console.error('处理标定结果失败:', error)
  }
}

// 模型构建
const buildModel = () => {
  if (isBuilding.value) return

  isBuilding.value = true
  buildProgress.value = 0

  const batteryParams = result.value['Battery parameters']
  const agingParams = result.value['Aging parameters']

  // 准备电化学参数
  const allParamsData = batteryParams.map((p) => ({
    param_key: p.param_name,
    min: p.min,
    max: p.max,
    init_value: p.value,
    ...(p.is_recommended ? { is_recommended: true } : {}),
  }))

  // 模拟进度更新
  const interval = setInterval(() => {
    buildProgress.value += Math.floor(Math.random() * 10) + 1

    if (buildProgress.value >= 100) {
      buildProgress.value = 100
      clearInterval(interval)

      // 延迟一会儿显示完成状态
      setTimeout(() => {
        isBuilding.value = false
        modelBuilt.value = true

        // 保存模型构建状态
        saveNodeParams(props.nodeData as any, {
          modelBuilt: true,
          all_params: all_params.value ? all_params.value : JSON.stringify(allParamsData),
          elec_params: JSON.stringify(
            batteryParams.reduce((obj, p) => {
              obj[p.param_name] = p.value
              return obj
            }, {}),
          ),
          aging_params: JSON.stringify(
            agingParams.reduce((obj, p) => {
              obj[p.param_name] = p.value
              return obj
            }, {}),
          ),
        })

        toast.success('模型构建成功', {
          description: '电池模型已成功构建完成',
        })
      }, 500)
    }
  }, 200)
}

// 处理修改模型
const handleEditModel = async () => {
  modelBuilt.value = false

  // 保存模型构建状态
  try {
    await saveNodeParams(props.nodeData as any, {
      modelBuilt: false,
    })
  } catch (error) {
    console.error('保存模型状态失败:', error)
  }

  toast.info('已进入编辑模式', {
    description: '您可以修改参数并重新构建模型',
  })
}

// 处理文件数据
const handleFileData = async ({ buffer }) => {
  try {
    // 使用 msgpack 解码
    const decodedData = decode(new Uint8Array(buffer))

    // 解码 base64 字符串
    const jsonString = decodeURIComponent(escape(atob(decodedData.data)))

    // 解析 JSON
    const fullData = JSON.parse(jsonString)

    // 只保留电化学参数、老化参数
    const processedData = {
      'Battery parameters': fullData['Battery parameters'],
      'Aging parameters': fullData['Aging parameters'],
    }

    result.value = processedData

    // 处理电池参数和老化参数，转换为指定格式
    const batteryParams = result.value['Battery parameters']
    const agingParams = result.value['Aging parameters']

    // 准备电化学参数的格式
    const allParamsData = batteryParams.map((p) => ({
      param_key: p.param_name,
      min: p.min,
      max: p.max,
      init_value: p.value,
      ...(p.is_recommended ? { is_recommended: true } : {}),
    }))

    // 保存解析后的数据
    await saveNodeParams(props.nodeData as any, {
      modelData: JSON.stringify(processedData),
      modelBuilt: false,
      all_params: JSON.stringify(allParamsData),
      elec_params: JSON.stringify(
        batteryParams.reduce((obj, p) => {
          obj[p.param_name] = p.value
          return obj
        }, {}),
      ),
      aging_params: JSON.stringify(
        agingParams.reduce((obj, p) => {
          obj[p.param_name] = p.value
          return obj
        }, {}),
      ),
    })
  } catch (error) {
    toast.error('文件解析失败', {
      description: error.message,
    })
    throw error
  }
}

// 进度处理
const handleProgress = (progress) => {
  // console.log('Upload progress:', progress)
}

// 上传完成处理函数
const handleUploadComplete = () => {
  dataLoaded.value = true
  toast.success('文件解析成功')
}

// 上传错误处理
const handleUploadError = (errorMsg) => {
  toast.error('文件上传错误', {
    description: errorMsg,
  })
}

// 打开编辑对话框
const openEditDialog = (type, index) => {
  if (!result.value || !result.value[type] || !result.value[type][index]) return

  currentParamType.value = type
  currentParamIndex.value = index

  // 深拷贝当前参数，避免直接修改原始数据
  currentParam.value = JSON.parse(JSON.stringify(result.value[type][index]))

  // 延迟打开对话框，确保值已经设置好
  nextTick(() => {
    isEditDialogOpen.value = true
  })
}

// 保存编辑
const handleSaveEdit = (value) => {
  if (currentParamType.value && currentParamIndex.value !== null && currentParam.value) {
    // 更新原始数据
    result.value[currentParamType.value][currentParamIndex.value].value = value

    toast.success('参数已更新', {
      description: `${currentParam.value.zh_description} 的值已更新为 ${value}`,
    })

    // 保存更新后的数据
    saveModelData()

    isEditDialogOpen.value = false
  }
}

// 重置数据
const resetData = async () => {
  result.value = {
    'Battery parameters': [],
    'Aging parameters': [],
  }
  dataLoaded.value = false
  modelBuilt.value = false

  try {
    await deleteNodeParams(props.nodeData as any)
  } catch (error) {
    console.error('删除节点参数失败:', error)
  }
}

// 监听结果变化，自动保存
watch(
  () => result.value,
  async (newVal) => {
    if (newVal && !isUploading.value) {
      await saveModelData()
    }
  },
  { deep: true },
)

useEventListener('beforeunload', handleBeforeUnload)

// 生命周期钩子
onMounted(async () => {
  await loadParams()
  await loadCalibrationParams()
  getCalibrationData()

  // 组件卸载时清理
  onUnmounted(() => {
    if (result.value && dataLoaded.value) {
      saveModelData()
    }
  })
})
</script>

<style lang="scss" scoped>
:deep(.input) {
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &[type='number'] {
    -moz-appearance: textfield;
  }
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background-color: var(--input-disabled-bg, rgba(0, 0, 0, 0.05));
  }
}
</style>
